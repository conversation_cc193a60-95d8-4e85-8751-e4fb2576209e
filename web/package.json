{"name": "final-full-stack-mern-advanced-web", "private": true, "version": "0.0.0", "type": "module", "author": "TrungQuanDev - <PERSON><PERSON><PERSON>", "description": "https://youtube.com/@trungquandev", "engines": {"node": ">=18.x"}, "scripts": {"dev": "cross-env BUILD_MODE=dev vite --host", "build": "cross-env BUILD_MODE=production vite build --base=./", "lint": "eslint src --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.0.8", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.1", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@mui/icons-material": "^5.11.16", "@mui/lab": "^5.0.0-alpha.163", "@mui/material": "^5.13.0", "@react-buddy/ide-toolbox": "^2.4.0", "@react-buddy/palette-mui": "^5.0.1", "@reduxjs/toolkit": "^2.0.1", "@uiw/react-md-editor": "^4.0.3", "axios": "^1.5.1", "cross-env": "^7.0.3", "lodash": "^4.17.21", "material-ui-confirm": "^3.0.9", "moment": "^2.30.1", "randomcolor": "^0.6.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.49.3", "react-redux": "^9.1.0", "react-router-dom": "^6.21.3", "react-toastify": "^9.1.3", "recharts": "^2.15.3", "redux-persist": "^6.0.0", "rehype-sanitize": "^6.0.0", "socket.io-client": "^4.7.4", "uuid": "^9.0.0"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@vitejs/plugin-react-swc": "^3.0.0", "eslint": "^8.38.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "vite": "^4.3.2", "vite-plugin-svgr": "^3.2.0"}}